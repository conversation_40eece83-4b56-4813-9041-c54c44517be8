This is pdfTeX, Version 3.141592653-2.6-1.40.27 (MiKTeX 25.4) (preloaded format=pdflatex 2025.6.21)  13 JUL 2025 22:55
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**./phase1_comprehensive_final.tex
(phase1_comprehensive_final.tex
LaTeX2e <2025-06-01>
L3 programming layer <2025-05-26>
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\article.cls
Document Class: article 2025/01/22 v1.4n Standard LaTeX document class
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\size11.clo
File: size11.clo 2025/01/22 v1.4n Standard LaTeX file (size option)
)
\c@part=\count275
\c@section=\count276
\c@subsection=\count277
\c@subsubsection=\count278
\c@paragraph=\count279
\c@subparagraph=\count280
\c@figure=\count281
\c@table=\count282
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen148
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\inputenc.sty
Package: inputenc 2024/02/08 v1.3d Input encoding file
\inpenc@prehook=\toks17
\inpenc@posthook=\toks18
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\fontenc.sty
Package: fontenc 2024/12/21 v2.1c Standard LaTeX package
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/geometry\geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks19
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/iftex\ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/iftex\iftex.sty
Package: iftex 2024/12/12 v1.0g TeX engine tests
))
\Gm@cnth=\count283
\Gm@cntv=\count284
\c@Gm@tempcnt=\count285
\Gm@bindingoffset=\dimen149
\Gm@wd@mp=\dimen150
\Gm@odd@mp=\dimen151
\Gm@even@mp=\dimen152
\Gm@layoutwidth=\dimen153
\Gm@layoutheight=\dimen154
\Gm@layouthoffset=\dimen155
\Gm@layoutvoffset=\dimen156
\Gm@dimlist=\toks20

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/geometry\geometry.cfg))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\graphicx.sty
Package: graphicx 2024/12/31 v1.2e Enhanced LaTeX Graphics (DPC,SPQR)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\graphics.sty
Package: graphics 2024/08/06 v1.4g Standard LaTeX Graphics (DPC,SPQR)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics-cfg\graphics.c
fg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 106.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics-def\pdftex.def
File: pdftex.def 2024/04/13 v1.2c Graphics/color driver for pdftex
))
\Gin@req@height=\dimen157
\Gin@req@width=\dimen158
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/float\float.sty
Package: float 2001/11/08 v1.3d Float enhancements (AL)
\c@float@type=\count286
\float@exts=\toks21
\float@box=\box53
\@float@everytoks=\toks22
\@floatcapt=\box54
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/caption\caption.sty
Package: caption 2023/08/05 v3.6o Customizing captions (AR)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/caption\caption3.sty
Package: caption3 2023/07/31 v2.4d caption3 kernel (AR)
\caption@tempdima=\dimen159
\captionmargin=\dimen160
\caption@leftmargin=\dimen161
\caption@rightmargin=\dimen162
\caption@width=\dimen163
\caption@indent=\dimen164
\caption@parindent=\dimen165
\caption@hangindent=\dimen166
Package caption Info: Standard document class detected.
)
\c@caption@flags=\count287
\c@continuedfloat=\count288
Package caption Info: float package is loaded.
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/caption\subcaption.sty
Package: subcaption 2023/07/28 v1.6b Sub-captions (AR)
Package caption Info: New subtype `subfigure' on input line 238.
\c@subfigure=\count289
Package caption Info: New subtype `subtable' on input line 238.
\c@subtable=\count290
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsmath.sty
Package: amsmath 2025/05/18 v2.17x AMS math features
\@mathmargin=\skip51

For additional information on amsmath, use the `?' option.
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amstext.sty
Package: amstext 2024/11/17 v2.01 AMS text

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks23
\ex@=\dimen167
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen168
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count291
LaTeX Info: Redefining \frac on input line 233.
\uproot@=\count292
\leftroot@=\count293
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count294
\DOTSCASE@=\count295
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box55
\strutbox@=\box56
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen169
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count296
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count297
\dotsspace@=\muskip17
\c@parentequation=\count298
\dspbrk@lvl=\count299
\tag@help=\toks24
\row@=\count300
\column@=\count301
\maxfields@=\count302
\andhelp@=\toks25
\eqnshift@=\dimen170
\alignsep@=\dimen171
\tagshift@=\dimen172
\tagwidth@=\dimen173
\totwidth@=\dimen174
\lineht@=\dimen175
\@envbody=\toks26
\multlinegap=\skip52
\multlinetaggap=\skip53
\mathdisplay@stack=\toks27
LaTeX Info: Redefining \[ on input line 2949.
LaTeX Info: Redefining \] on input line 2950.
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsfonts\amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsfonts\amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/booktabs\booktabs.sty
Package: booktabs 2020/01/12 v1.61803398 Publication quality tables
\heavyrulewidth=\dimen176
\lightrulewidth=\dimen177
\cmidrulewidth=\dimen178
\belowrulesep=\dimen179
\belowbottomsep=\dimen180
\aboverulesep=\dimen181
\abovetopsep=\dimen182
\cmidrulesep=\dimen183
\cmidrulekern=\dimen184
\defaultaddspace=\dimen185
\@cmidla=\count303
\@cmidlb=\count304
\@aboverulesep=\dimen186
\@belowrulesep=\dimen187
\@thisruleclass=\count305
\@lastruleclass=\count306
\@thisrulewidth=\dimen188
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/enumitem\enumitem.sty
Package: enumitem 2025/02/06 v3.11 Customized lists
\labelindent=\skip54
\enit@outerparindent=\dimen189
\enit@toks=\toks28
\enit@inbox=\box57
\enit@count@id=\count307
\enitdp@description=\count308
)
! Undefined control sequence.
<recently read> \usetikzlibrary 
                                
l.14 \usetikzlibrary
                    {shapes.geometric}
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.


! LaTeX Error: Missing \begin{document}.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.14 \usetikzlibrary{s
                      hapes.geometric}
You're in trouble here.  Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.

! Undefined control sequence.
l.15 \usetikzlibrary
                    {shapes}
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

! Undefined control sequence.
l.16 \usetikzlibrary
                    {positioning}
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgfplots\pgfplots.sty
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots\pgfplots.rev
ision.tex)
Package: pgfplots 2021/05/15 v1.18.1 Data Visualization (1.18.1)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/frontendlayer\tikz.
sty
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/basiclayer\pgf.sty
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/utilities\pgfrcs.st
y
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/utilities\pgfutil
-common.tex
\pgfutil@everybye=\toks29
\pgfutil@tempdima=\dimen190
\pgfutil@tempdimb=\dimen191
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/utilities\pgfutil
-latex.def
\pgfutil@abb=\box58
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/utilities\pgfrcs.
code.tex
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf\pgf.revision.tex)
Package: pgfrcs 2023-01-15 v3.1.10 (3.1.10)
))
Package: pgf 2023-01-15 v3.1.10 (3.1.10)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/basiclayer\pgfcore.
sty
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/systemlayer\pgfsys.
sty
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/systemlayer\pgfsy
s.code.tex
Package: pgfsys 2023-01-15 v3.1.10 (3.1.10)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/utilities\pgfkeys
.code.tex
\pgfkeys@pathtoks=\toks30
\pgfkeys@temptoks=\toks31

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/utilities\pgfkeys
libraryfiltered.code.tex
\pgfkeys@tmptoks=\toks32
))
\pgf@x=\dimen192
\pgf@y=\dimen193
\pgf@xa=\dimen194
\pgf@ya=\dimen195
\pgf@xb=\dimen196
\pgf@yb=\dimen197
\pgf@xc=\dimen198
\pgf@yc=\dimen199
\pgf@xd=\dimen256
\pgf@yd=\dimen257
\w@pgf@writea=\write3
\r@pgf@reada=\read2
\c@pgf@counta=\count309
\c@pgf@countb=\count310
\c@pgf@countc=\count311
\c@pgf@countd=\count312
\t@pgf@toka=\toks33
\t@pgf@tokb=\toks34
\t@pgf@tokc=\toks35
\pgf@sys@id@count=\count313

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/systemlayer\pgf.c
fg
File: pgf.cfg 2023-01-15 v3.1.10 (3.1.10)
)
Driver file for pgf: pgfsys-pdftex.def

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/systemlayer\pgfsy
s-pdftex.def
File: pgfsys-pdftex.def 2023-01-15 v3.1.10 (3.1.10)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/systemlayer\pgfsy
s-common-pdf.def
File: pgfsys-common-pdf.def 2023-01-15 v3.1.10 (3.1.10)
)))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/systemlayer\pgfsy
ssoftpath.code.tex
File: pgfsyssoftpath.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfsyssoftpath@smallbuffer@items=\count314
\pgfsyssoftpath@bigbuffer@items=\count315
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/systemlayer\pgfsy
sprotocol.code.tex
File: pgfsysprotocol.code.tex 2023-01-15 v3.1.10 (3.1.10)
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/xcolor\xcolor.sty
Package: xcolor 2024/09/29 v3.02 LaTeX color extensions (UK)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics-cfg\color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 274.
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1349.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1353.
Package xcolor Info: Model `RGB' extended on input line 1365.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1367.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1368.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1371.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1372.
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
e.code.tex
Package: pgfcore 2023-01-15 v3.1.10 (3.1.10)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmath.code
.tex
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathutil.
code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathparse
r.code.tex
\pgfmath@dimen=\dimen258
\pgfmath@count=\count316
\pgfmath@box=\box59
\pgfmath@toks=\toks36
\pgfmath@stack@operand=\toks37
\pgfmath@stack@operation=\toks38
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfunct
ions.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfunct
ions.basic.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfunct
ions.trigonometric.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfunct
ions.random.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfunct
ions.comparison.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfunct
ions.base.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfunct
ions.round.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfunct
ions.misc.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfunct
ions.integerarithmetics.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathcalc.
code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfloat
.code.tex
\c@pgfmathroundto@lastzeros=\count317
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfint.code.
tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
epoints.code.tex
File: pgfcorepoints.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@picminx=\dimen259
\pgf@picmaxx=\dimen260
\pgf@picminy=\dimen261
\pgf@picmaxy=\dimen262
\pgf@pathminx=\dimen263
\pgf@pathmaxx=\dimen264
\pgf@pathminy=\dimen265
\pgf@pathmaxy=\dimen266
\pgf@xx=\dimen267
\pgf@xy=\dimen268
\pgf@yx=\dimen269
\pgf@yy=\dimen270
\pgf@zx=\dimen271
\pgf@zy=\dimen272
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
epathconstruct.code.tex
File: pgfcorepathconstruct.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@path@lastx=\dimen273
\pgf@path@lasty=\dimen274
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
epathusage.code.tex
File: pgfcorepathusage.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@shorten@end@additional=\dimen275
\pgf@shorten@start@additional=\dimen276
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
escopes.code.tex
File: pgfcorescopes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfpic=\box60
\pgf@hbox=\box61
\pgf@layerbox@main=\box62
\pgf@picture@serial@count=\count318
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
egraphicstate.code.tex
File: pgfcoregraphicstate.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgflinewidth=\dimen277
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
etransformations.code.tex
File: pgfcoretransformations.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@pt@x=\dimen278
\pgf@pt@y=\dimen279
\pgf@pt@temp=\dimen280
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
equick.code.tex
File: pgfcorequick.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
eobjects.code.tex
File: pgfcoreobjects.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
epathprocessing.code.tex
File: pgfcorepathprocessing.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
earrows.code.tex
File: pgfcorearrows.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfarrowsep=\dimen281
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
eshade.code.tex
File: pgfcoreshade.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@max=\dimen282
\pgf@sys@shading@range@num=\count319
\pgf@shadingcount=\count320
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
eimage.code.tex
File: pgfcoreimage.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
eexternal.code.tex
File: pgfcoreexternal.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfexternal@startupbox=\box63
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
elayers.code.tex
File: pgfcorelayers.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
etransparency.code.tex
File: pgfcoretransparency.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
epatterns.code.tex
File: pgfcorepatterns.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
erdf.code.tex
File: pgfcorerdf.code.tex 2023-01-15 v3.1.10 (3.1.10)
)))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/modules\pgfmodule
shapes.code.tex
File: pgfmoduleshapes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfnodeparttextbox=\box64
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/modules\pgfmodule
plot.code.tex
File: pgfmoduleplot.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/compatibility\pgfco
mp-version-0-65.sty
Package: pgfcomp-version-0-65 2023-01-15 v3.1.10 (3.1.10)
\pgf@nodesepstart=\dimen283
\pgf@nodesepend=\dimen284
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/compatibility\pgfco
mp-version-1-18.sty
Package: pgfcomp-version-1-18 2023-01-15 v3.1.10 (3.1.10)
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/utilities\pgffor.st
y
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/utilities\pgfkeys.s
ty
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/utilities\pgfkeys
.code.tex))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/math\pgfmath.sty
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmath.code
.tex))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/utilities\pgffor.
code.tex
Package: pgffor 2023-01-15 v3.1.10 (3.1.10)
\pgffor@iter=\dimen285
\pgffor@skip=\dimen286
\pgffor@stack=\toks39
\pgffor@toks=\toks40
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/frontendlayer/tik
z\tikz.code.tex
Package: tikz 2023-01-15 v3.1.10 (3.1.10)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/libraries\pgflibr
aryplothandlers.code.tex
File: pgflibraryplothandlers.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@plot@mark@count=\count321
\pgfplotmarksize=\dimen287
)
\tikz@lastx=\dimen288
\tikz@lasty=\dimen289
\tikz@lastxsaved=\dimen290
\tikz@lastysaved=\dimen291
\tikz@lastmovetox=\dimen292
\tikz@lastmovetoy=\dimen293
\tikzleveldistance=\dimen294
\tikzsiblingdistance=\dimen295
\tikz@figbox=\box65
\tikz@figbox@bg=\box66
\tikz@tempbox=\box67
\tikz@tempbox@bg=\box68
\tikztreelevel=\count322
\tikznumberofchildren=\count323
\tikznumberofcurrentchild=\count324
\tikz@fig@count=\count325

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/modules\pgfmodule
matrix.code.tex
File: pgfmodulematrix.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfmatrixcurrentrow=\count326
\pgfmatrixcurrentcolumn=\count327
\pgf@matrix@numberofcolumns=\count328
)
\tikz@expandcount=\count329

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/frontendlayer/tik
z/libraries\tikzlibrarytopaths.code.tex
File: tikzlibrarytopaths.code.tex 2023-01-15 v3.1.10 (3.1.10)
)))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots\pgfplots.cod
e.tex
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots\pgfplotscore
.code.tex
\t@pgfplots@toka=\toks41
\t@pgfplots@tokb=\toks42
\t@pgfplots@tokc=\toks43
\pgfplots@tmpa=\dimen296
\c@pgfplots@coordindex=\count330
\c@pgfplots@scanlineindex=\count331

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots/sys\pgfplots
sysgeneric.code.tex))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots/libs\pgfplot
slibrary.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots/oldpgfcompat
ib\pgfplotsoldpgfsupp_loader.code.tex
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/libraries\pgflibr
aryfpu.code.tex)
Package pgfplots: loading complementary utilities for your pgf version...
\t@pgf@toka=\toks44
\t@pgf@tokb=\toks45
\t@pgf@tokc=\toks46

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots/oldpgfcompat
ib\pgfplotsoldpgfsupp_pgfutil-common-lists.tex))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots/util\pgfplot
sutil.code.tex
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots/liststructur
e\pgfplotsliststructure.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots/liststructur
e\pgfplotsliststructureext.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots/liststructur
e\pgfplotsarray.code.tex
\c@pgfplotsarray@tmp=\count332
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots/liststructur
e\pgfplotsmatrix.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots/numtable\pgf
plotstableshared.code.tex
\c@pgfplotstable@counta=\count333
\t@pgfplotstable@a=\toks47
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots/liststructur
e\pgfplotsdeque.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots/util\pgfplot
sbinary.code.tex
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots/util\pgfplot
sbinary.data.code.tex))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots/util\pgfplot
sutil.verb.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots/libs\pgflibr
arypgfplots.surfshading.code.tex
\c@pgfplotslibrarysurf@no=\count334

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots/sys\pgflibra
rypgfplots.surfshading.pgfsys-pdftex.def)))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots/util\pgfplot
scolormap.code.tex
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots/util\pgfplot
scolor.code.tex))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots\pgfplotsstac
kedplots.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots\pgfplotsplot
handlers.code.tex
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots\pgfplotsmesh
plothandler.code.tex
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots\pgfplotsmesh
plotimage.code.tex)))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots\pgfplots.sca
ling.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots\pgfplotscoor
dprocessing.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots\pgfplots.err
orbars.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots\pgfplots.mar
kers.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots\pgfplotstick
s.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots\pgfplots.pat
hs.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/frontendlayer/tik
z/libraries\tikzlibrarydecorations.code.tex
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/modules\pgfmodule
decorations.code.tex
\pgfdecoratedcompleteddistance=\dimen297
\pgfdecoratedremainingdistance=\dimen298
\pgfdecoratedinputsegmentcompleteddistance=\dimen299
\pgfdecoratedinputsegmentremainingdistance=\dimen300
\pgf@decorate@distancetomove=\dimen301
\pgf@decorate@repeatstate=\count335
\pgfdecorationsegmentamplitude=\dimen302
\pgfdecorationsegmentlength=\dimen303
)
\tikz@lib@dec@box=\box69
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/frontendlayer/tik
z/libraries\tikzlibrarydecorations.pathmorphing.code.tex
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/libraries/decorat
ions\pgflibrarydecorations.pathmorphing.code.tex))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/frontendlayer/tik
z/libraries\tikzlibrarydecorations.pathreplacing.code.tex
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/libraries/decorat
ions\pgflibrarydecorations.pathreplacing.code.tex))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots/libs\tikzlib
rarypgfplots.contourlua.code.tex)
\pgfplots@numplots=\count336
\pgfplots@xmin@reg=\dimen304
\pgfplots@xmax@reg=\dimen305
\pgfplots@ymin@reg=\dimen306
\pgfplots@ymax@reg=\dimen307
\pgfplots@zmin@reg=\dimen308
\pgfplots@zmax@reg=\dimen309
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/frontendlayer/tik
z/libraries\tikzlibraryplotmarks.code.tex
File: tikzlibraryplotmarks.code.tex 2023-01-15 v3.1.10 (3.1.10)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/libraries\pgflibr
aryplotmarks.code.tex
File: pgflibraryplotmarks.code.tex 2023-01-15 v3.1.10 (3.1.10)
)))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/fancyhdr\fancyhdr.sty
Package: fancyhdr 2025/02/07 v5.2 Extensive control of page headers and footers

\f@nch@headwidth=\skip55
\f@nch@offset@elh=\skip56
\f@nch@offset@erh=\skip57
\f@nch@offset@olh=\skip58
\f@nch@offset@orh=\skip59
\f@nch@offset@elf=\skip60
\f@nch@offset@erf=\skip61
\f@nch@offset@olf=\skip62
\f@nch@offset@orf=\skip63
\f@nch@height=\skip64
\f@nch@footalignment=\skip65
\f@nch@widthL=\skip66
\f@nch@widthC=\skip67
\f@nch@widthR=\skip68
\@temptokenb=\toks48
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/titlesec\titlesec.sty
Package: titlesec 2025/01/04 v2.17 Sectioning titles
\ttl@box=\box70
\beforetitleunit=\skip69
\aftertitleunit=\skip70
\ttl@plus=\dimen310
\ttl@minus=\dimen311
\ttl@toksa=\toks49
\titlewidth=\dimen312
\titlewidthlast=\dimen313
\titlewidthfirst=\dimen314
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/tools\longtable.sty
Package: longtable 2024-12-18 v4.23 Multi-page Table package (DPC)
\LTleft=\skip71
\LTright=\skip72
\LTpre=\skip73
\LTpost=\skip74
\LTchunksize=\count337
\LTcapwidth=\dimen315
\LT@head=\box71
\LT@firsthead=\box72
\LT@foot=\box73
\LT@lastfoot=\box74
\LT@gbox=\box75
\LT@cols=\count338
\LT@rows=\count339
\c@LT@tables=\count340
\c@LT@chunks=\count341
\LT@p@ftn=\toks50
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/multirow\multirow.sty
Package: multirow 2024/11/12 v2.9 Span multiple rows of a table
\multirow@colwidth=\skip75
\multirow@cntb=\count342
\multirow@dima=\skip76
\bigstrutjot=\dimen316
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/parskip\parskip.sty
Package: parskip 2021-03-14 v2.0h non-zero parskip adjustments

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/kvoptions\kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/ltxcmds\ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/kvsetkeys\kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/etoolbox\etoolbox.sty
Package: etoolbox 2025/02/11 v2.5l e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count343
)) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/url\url.sty
\Urlmuskip=\muskip18
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
! Undefined control sequence.
l.53 \hypersetup
                {
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.


! LaTeX Error: Missing \begin{document}.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.54 c
      olorlinks=true, linkcolor=blue, filecolor=magenta, urlcolor=cyan, pdft...

You're in trouble here.  Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.


Overfull \hbox (28.39928pt too wide) in paragraph at lines 54--56
[]\T1/cmr/m/n/10.95 colorlinks=true, link-color=blue, file-color=magenta, url-c
olor=cyan, pdfti-tle=Phase
 []

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\hyperref.sty
Package: hyperref 2025-05-20 v7.01m Hypertext links for LaTeX

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/kvdefinekeys\kvdefine
keys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pdfescape\pdfescape.s
ty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pdftexcmds\pdftexcmds
.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO
)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/infwarerr\infwarerr.s
ty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode found.
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hycolor\hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\nameref.sty
Package: nameref 2023-11-26 v2.56 Cross-referencing by name of section

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/refcount\refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/gettitlestring\gettit
lestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)
)
\c@section@level=\count344
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/stringenc\stringenc.s
ty
Package: stringenc 2019/11/29 v1.12 Convert strings between diff. encodings (HO
)
)
\@linkdim=\dimen317
\Hy@linkcounter=\count345
\Hy@pagecounter=\count346
 (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\pd1enc.def
File: pd1enc.def 2025-05-20 v7.01m Hyperref: PDFDocEncoding definition (HO)
Now handling font encoding PD1 ...
... no UTF-8 mapping file for font encoding PD1
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/intcalc\intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count347
 (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\puenc.def
File: puenc.def 2025-05-20 v7.01m Hyperref: PDF Unicode definition (HO)
Now handling font encoding PU ...
... no UTF-8 mapping file for font encoding PU
)
Package hyperref Info: Hyper figures OFF on input line 4157.
Package hyperref Info: Link nesting OFF on input line 4162.
Package hyperref Info: Hyper index ON on input line 4165.
Package hyperref Info: Plain pages OFF on input line 4172.
Package hyperref Info: Backreferencing OFF on input line 4177.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4424.
\c@Hy@tempcnt=\count348
LaTeX Info: Redefining \url on input line 4763.
\XeTeXLinkMargin=\dimen318

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/bitset\bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/bigintcalc\bigintcalc
.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO
)
))
\Fld@menulength=\count349
\Field@Width=\dimen319
\Fld@charsize=\dimen320
Package hyperref Info: Hyper figures OFF on input line 6042.
Package hyperref Info: Link nesting OFF on input line 6047.
Package hyperref Info: Hyper index ON on input line 6050.
Package hyperref Info: backreferencing OFF on input line 6057.
Package hyperref Info: Link coloring OFF on input line 6062.
Package hyperref Info: Link coloring with OCG OFF on input line 6067.
Package hyperref Info: PDF/A mode OFF on input line 6072.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\atbegshi-ltx.sty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
)
\Hy@abspage=\count350
\c@Item=\count351
\c@Hfootnote=\count352
)
Package hyperref Info: Driver (autodetected): hpdftex.
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\hpdftex.def
File: hpdftex.def 2025-05-20 v7.01m Hyperref driver for pdfTeX
\Fld@listcount=\count353
\c@bookmark@seq@number=\count354

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/rerunfilecheck\rerunfil
echeck.sty
Package: rerunfilecheck 2022-07-10 v1.10 Rerun checks for auxiliary files (HO)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\atveryend-ltx.sty
Package: atveryend-ltx 2020/08/19 v1.0a Emulation of the original atveryend pac
kage
with kernel methods
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/uniquecounter\uniquec
ounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 2
85.
)
\Hy@SectionHShift=\skip77
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/l3backend\l3backend-pdf
tex.def
File: l3backend-pdftex.def 2025-04-14 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count355
) (phase1_comprehensive_final.aux)
\openout1 = `phase1_comprehensive_final.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 65.
LaTeX Font Info:    ... okay on input line 65.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 65.
LaTeX Font Info:    ... okay on input line 65.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 65.
LaTeX Font Info:    ... okay on input line 65.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 65.
LaTeX Font Info:    ... okay on input line 65.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 65.
LaTeX Font Info:    ... okay on input line 65.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 65.
LaTeX Font Info:    ... okay on input line 65.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 65.
LaTeX Font Info:    ... okay on input line 65.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 65.
LaTeX Font Info:    ... okay on input line 65.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 65.
LaTeX Font Info:    ... okay on input line 65.

*geometry* driver: auto-detecting
*geometry* detected driver: pdftex
*geometry* verbose mode - [ preamble ] result:
* driver: pdftex
* paper: a4paper
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(72.26999pt, 452.9679pt, 72.26999pt)
* v-part:(T,H,B)=(72.26999pt, 700.50687pt, 72.26999pt)
* \paperwidth=597.50787pt
* \paperheight=845.04684pt
* \textwidth=452.9679pt
* \textheight=700.50687pt
* \oddsidemargin=0.0pt
* \evensidemargin=0.0pt
* \topmargin=-37.0pt
* \headheight=14.0pt
* \headsep=25.0pt
* \topskip=11.0pt
* \footskip=30.0pt
* \marginparwidth=50.0pt
* \marginparsep=10.0pt
* \columnsep=10.0pt
* \skip\footins=10.0pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)


(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/context/base/mkii\supp-pdf.mk
ii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count356
\scratchdimen=\dimen321
\scratchbox=\box76
\nofMPsegments=\count357
\nofMParguments=\count358
\everyMPshowfont=\toks51
\MPscratchCnt=\count359
\MPscratchDim=\dimen322
\MPnumerator=\count360
\makeMPintoPDFobject=\count361
\everyMPtoPDFconversion=\toks52
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/epstopdf-pkg\epstopdf-b
ase.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 4
85.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/00miktex\epstopdf-sys.c
fg
File: epstopdf-sys.cfg 2021/03/18 v2.0 Configuration of epstopdf for MiKTeX
))
Package caption Info: Begin \AtBeginDocument code.
Package caption Info: hyperref package is loaded.
Package caption Info: longtable package is loaded.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/caption\ltcaption.sty
Package: ltcaption 2021/01/08 v1.4c longtable captions (AR)
)
Package caption Info: End \AtBeginDocument code.

Package pgfplots notification 'compat/show suggested version=true': you might b
enefit from \pgfplotsset{compat=1.18} (current compat level: 1.17).

Package hyperref Info: Link coloring OFF on input line 65.
(phase1_comprehensive_final.out) (phase1_comprehensive_final.out)
\@outlinefile=\write4
\openout4 = `phase1_comprehensive_final.out'.



[1{C:/Users/<USER>/AppData/Local/MiKTeX/fonts/map/pdftex/pdftex.map}

]


pdfTeX warning (ext4): destination with the same identifier (name{page.1}) has 
been already used, duplicate ignored
<to be read again> 
                   \relax 
l.77 \end{titlepage}
                     [1] (phase1_comprehensive_final.toc
LaTeX Font Info:    Trying to load font information for U+msa on input line 5.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsfonts\umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 5.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsfonts\umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
))
\tf@toc=\write5
\openout5 = `phase1_comprehensive_final.toc'.




pdfTeX warning (ext4): destination with the same identifier (name{page.1}) has 
been already used, duplicate ignored
<to be read again> 
                   \relax 
l.88 \newpage
              [1]
<pk_profiles.png, id=63, 858.5676pt x 353.1594pt>
File: pk_profiles.png Graphic file (type png)
<use pk_profiles.png>
Package pdftex.def Info: pk_profiles.png  used on input line 107.
(pdftex.def)             Requested size: 407.66833pt x 167.68777pt.


[2]

[3 <./pk_profiles.png>]

[4]

[5]

[6]

[7]

[8]
<integrated_phase1_design.png, id=112, 853.9905pt x 579.6054pt>
File: integrated_phase1_design.png Graphic file (type png)
<use integrated_phase1_design.png>
Package pdftex.def Info: integrated_phase1_design.png  used on input line 176.
(pdftex.def)             Requested size: 407.66833pt x 276.68625pt.


[9]

[10 <./integrated_phase1_design.png>]
Overfull \hbox (1.9943pt too wide) in paragraph at lines 207--208
[]\T1/cmr/bx/n/10.95 First-in-Human Sin-gle Dose Es-ca-la-tion \T1/cmr/m/n/10.9
5 (Healthy vol-un-teers, placebo-controlled SAD). 
 []



[11]
<dose_escalation_methods.png, id=129, 1004.553pt x 425.1885pt>
File: dose_escalation_methods.png Graphic file (type png)
<use dose_escalation_methods.png>
Package pdftex.def Info: dose_escalation_methods.png  used on input line 224.
(pdftex.def)             Requested size: 407.66833pt x 172.55074pt.
<figure5_safety_monitoring.png, id=130, 1149.093pt x 860.013pt>
File: figure5_safety_monitoring.png Graphic file (type png)
<use figure5_safety_monitoring.png>
Package pdftex.def Info: figure5_safety_monitoring.png  used on input line 238.

(pdftex.def)             Requested size: 430.31812pt x 322.0579pt.

Runaway argument?
{figure> 
! Paragraph ended before \end  was complete.
<to be read again> 
                   \par 
l.242 
      
I suspect you've forgotten a `}', causing me to apply this
control sequence to too much text. How can we recover?
My plan is to forget the whole thing and hope for the best.


Underfull \hbox (badness 10000) in paragraph at lines 243--243
|[]\T1/cmr/bx/n/12 Determining the First-in-Human Start-ing Dose|
 []


Underfull \hbox (badness 10000) in paragraph at lines 247--247
|[]\T1/cmr/bx/n/12 Commonly Used Ap-proaches|
 []


Underfull \hbox (badness 10000) in paragraph at lines 259--259
|[]\T1/cmr/bx/n/12 Dose Es-ca-la-tion Strat-egy|
 []


Underfull \hbox (badness 10000) in paragraph at lines 271--271
|[]\T1/cmr/bx/n/12 Special Case ^^U On-col-ogy Start-ing Doses|
 []


Underfull \hbox (badness 10000) in paragraph at lines 275--275
|[]\T1/cmr/bx/n/12 Dose Se-lec-tion for Phase 2|
 []


Underfull \hbox (badness 10000) in paragraph at lines 295--295
|[]\T1/cmr/bx/n/14.4 Statistical Anal-y-sis in Phase 1 Tri-als|
 []

<figure4_statistical_models.png, id=131, 1149.093pt x 860.013pt>
File: figure4_statistical_models.png Graphic file (type png)
<use figure4_statistical_models.png>
Package pdftex.def Info: figure4_statistical_models.png  used on input line 312
.
(pdftex.def)             Requested size: 385.02547pt x 288.16191pt.
<figure7_pkpd_relationships.png, id=132, 1293.633pt x 974.9223pt>
File: figure7_pkpd_relationships.png Graphic file (type png)
<use figure7_pkpd_relationships.png>
Package pdftex.def Info: figure7_pkpd_relationships.png  used on input line 319
.
(pdftex.def)             Requested size: 385.02547pt x 290.15828pt.
<figure6_dose_translation.png, id=133, 1149.093pt x 932.283pt>
File: figure6_dose_translation.png Graphic file (type png)
<use figure6_dose_translation.png>
Package pdftex.def Info: figure6_dose_translation.png  used on input line 326.
(pdftex.def)             Requested size: 407.66833pt x 330.75652pt.

Underfull \hbox (badness 10000) in paragraph at lines 331--331
|[]\T1/cmr/bx/n/14.4 Regulatory Frame-work and Guide-lines
 []


Underfull \hbox (badness 10000) in paragraph at lines 331--331
\T1/cmr/bx/n/14.4 for Phase 1 Tri-als (US, EU, In-dia)|
 []


Underfull \hbox (badness 10000) in paragraph at lines 335--335
|[]\T1/cmr/bx/n/12 United States (FDA Reg-u-la-tions)|
 []


Underfull \hbox (badness 10000) in paragraph at lines 350--350
|[]\T1/cmr/bx/n/12 European Union (EMA and Na-tional Agen-cies)|
 []


Underfull \hbox (badness 1565) in paragraph at lines 366--366
|[]\T1/cmr/bx/n/12 India (CD-SCO and New Drugs and Clin-i-cal Tri-als Rules 201
9)|
 []


Underfull \hbox (badness 10000) in paragraph at lines 385--385
|[]\T1/cmr/bx/n/14.4 Disasters and Lessons Learned in Phase 1 Tri-als|
 []


Underfull \hbox (badness 10000) in paragraph at lines 389--389
|[]\T1/cmr/bx/n/12 TGN1412 Trial Dis-as-ter (2006)|
 []


Underfull \hbox (badness 10000) in paragraph at lines 399--399
|[]\T1/cmr/bx/n/12 BIA 10-2474 Trial Dis-as-ter (2016)|
 []


Underfull \hbox (badness 10000) in paragraph at lines 409--409
|[]\T1/cmr/bx/n/12 Regulatory and Eth-i-cal Reper-cus-sions|
 []


Underfull \hbox (badness 10000) in paragraph at lines 419--419
|[]\T1/cmr/bx/n/12 Changes in Trial De-sign and Con-duct|
 []


LaTeX Warning: File `figure8_risk_assessment.png' not found on input line 431.


! Package pdftex.def Error: File `figure8_risk_assessment.png' not found: using
 draft setting.

See the pdftex.def package documentation for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.431 ...0\textwidth]{figure8_risk_assessment.png}
                                                  
Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.


Underfull \hbox (badness 10000) in paragraph at lines 436--436
|[]\T1/cmr/bx/n/14.4 Limitations of Phase 1 Tri-als|
 []


Underfull \hbox (badness 10000) in paragraph at lines 438--438
|[]\T1/cmr/bx/n/12 Inherent Lim-i-ta-tions of Phase 1 Tri-als|
 []


LaTeX Warning: Reference `fig:dose_escalation' on page 12 undefined on input li
ne 441.


Underfull \hbox (badness 10000) in paragraph at lines 461--461
|[]\T1/cmr/bx/n/14.4 Conclusion|
 []


Underfull \hbox (badness 10000) in paragraph at lines 473--473
|[]\T1/cmr/bx/n/14.4 References|
 []


Underfull \hbox (badness 10000) in paragraph at lines 475--475
|\T1/cmr/bx/n/14.4 References|
 []


! LaTeX Error: \begin{figure} on input line 236 ended by \end{document}.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.480 \end{document}
                    
Your command was ignored.
Type  I <command> <return>  to replace it with another command,
or  <return>  to continue without it.

(phase1_comprehensive_final.aux)
 ***********
LaTeX2e <2025-06-01>
L3 programming layer <2025-05-26>
 ***********


LaTeX Warning: There were undefined references.

Package rerunfilecheck Info: File `phase1_comprehensive_final.out' has not chan
ged.
(rerunfilecheck)             Checksum: 4E2119B7F99469EC310626BF77999B91;1725.
! You can't use `\end' in internal vertical mode.
\enddocument ...cument/end}\deadcycles \z@ \@@end 
                                                  
l.480 \end{document}
                    
Sorry, but I'm not programmed to handle this case;
I'll just pretend that you didn't ask for it.
If you're in the wrong mode, you might be able to
return to the right one by typing `I}' or `I$' or `I\par'.


! LaTeX Error: \begin{figure} on input line 236 ended by \end{document}.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.480 \end{document}
                    
Your command was ignored.
Type  I <command> <return>  to replace it with another command,
or  <return>  to continue without it.

! Missing } inserted.
<inserted text> 
                }
l.480 \end{document}
                    
I've inserted something that you may have forgotten.
(See the <inserted text> above.)
With luck, this will get me unwedged. But if you
really didn't forget anything, try typing `2' now; then
my insertion and my current dilemma will both disappear.

)
! Emergency stop.
<*> ./phase1_comprehensive_final.tex
                                    
*** (job aborted, no legal \end found)

 
Here is how much of TeX's memory you used:
 33666 strings out of 468225
 779300 string characters out of 5432973
 1351183 words of memory out of 5000000
 61644 multiletter control sequences out of 15000+600000
 640528 words of font info for 71 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 99i,11n,105p,1687b,712s stack positions out of 10000i,1000n,20000p,200000b,200000s
!  ==> Fatal error occurred, no output PDF file produced!
